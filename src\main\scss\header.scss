.navbar {
  border-top: 4px solid #6db33f;
  background-color: #34302d;
  margin-bottom: 0px;
  border-bottom: 0;
  border-left: 0;
  border-right: 0;
}

.navbar a.navbar-brand {
  background: url("../images/spring-logo-dataflow.png") -1px -1px no-repeat;
  margin: 12px 0 6px;
  width: 229px;
  height: 46px;
  display: inline-block;
  text-decoration: none;
  padding: 0;
}

.navbar a.navbar-brand span {
  display: block;
  width: 229px;
  height: 46px;
  background: url("../images/spring-logo-dataflow.png") -1px -48px no-repeat;
  opacity: 0;
  -moz-transition: opacity 0.12s ease-in-out;
  -webkit-transition: opacity 0.12s ease-in-out;
  -o-transition: opacity 0.12s ease-in-out;
}

.navbar a:hover.navbar-brand span {
  opacity: 1;
}

.navbar li > a, .navbar-text {
  font-family: "montserratregular", sans-serif;
  text-shadow: none;
  font-size: 14px;

/* line-height: 14px; */
  padding: 28px 20px;
  transition: all 0.15s;
  -webkit-transition: all 0.15s;
  -moz-transition: all 0.15s;
  -o-transition: all 0.15s;
  -ms-transition: all 0.15s;
}

.navbar li > a {
  text-transform: uppercase;
}

.navbar .navbar-text {
  margin-top: 0;
  margin-bottom: 0;
}
.navbar li:hover > a {
  color: #eeeeee;
  background-color: #6db33f;
}

.navbar-toggle {
  border-width: 0;

  .icon-bar + .icon-bar {
    margin-top: 3px;
  }
  .icon-bar {
    width: 19px;
    height: 3px;
  }

}
