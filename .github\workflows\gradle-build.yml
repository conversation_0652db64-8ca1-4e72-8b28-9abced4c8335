# This workflow will build a Java project with <PERSON>radle, and cache/restore any dependencies to improve the workflow execution time
# For more information see: https://docs.github.com/en/actions/use-cases-and-examples/building-and-testing/building-and-testing-java-with-gradle

name: Java CI with <PERSON>rad<PERSON>

on:
  push:
    branches: [ main ]
  pull_request:
    branches: [ main ]

jobs:
  build:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        java: [ '17' ]

    steps:
      - uses: actions/checkout@v4
      - name: Set up JDK ${{matrix.java}}
        uses: actions/setup-java@v4
        with:
          java-version: ${{matrix.java}}
          distribution: 'adopt'
          cache: maven
      - name: Setup Gradle
        uses: gradle/actions/setup-gradle@v4
      - name: Build with Gradle
        run: ./gradlew build
