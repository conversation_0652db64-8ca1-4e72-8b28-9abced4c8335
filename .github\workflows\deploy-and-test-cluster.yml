name: Deploy and Test Cluster

on:
  push:
    branches: [main]
    paths:
      - 'k8s/**'
  pull_request:
    branches: [main]
    paths:
      - 'k8s/**'

jobs:
  deploy-and-test-cluster:
    runs-on: ubuntu-latest
    steps:
      - name: Check out the repository
        uses: actions/checkout@v2

      - name: Create k8s Kind Cluster
        uses: helm/kind-action@v1

      - name: Deploy application
        run: |
          kubectl apply -f k8s/

      - name: Wait for Pods to be ready
        run: |
          kubectl wait --for=condition=ready pod -l app=demo-db --timeout=180s
          kubectl wait --for=condition=ready pod -l app=petclinic --timeout=180s

