<html>
<body>
  <form>
    <th:block th:fragment="input (label, name, type)">
      <div th:with="valid=${!#fields.hasErrors(name)}"
        th:class="${'form-group' + (valid ? '' : ' has-error')}"
        class="form-group">
        <label class="col-sm-2 control-label" th:text="${label}">Label</label>
        <div class="col-sm-10">
            <div th:switch="${type}">
                <input th:case="'text'" class="form-control" type="text" th:field="*{__${name}__}" />
                <input th:case="'date'" class="form-control" type="date" th:field="*{__${name}__}"/>
            </div>
          <span th:if="${valid}"
            class="fa fa-ok form-control-feedback"
            aria-hidden="true"></span>
          <th:block th:if="${!valid}">
            <span
              class="fa fa-remove form-control-feedback"
              aria-hidden="true"></span>
            <span class="help-inline" th:errors="*{__${name}__}">Error</span>
          </th:block>
        </div>
      </div>
    </th:block>
  </form>
</body>
</html>
