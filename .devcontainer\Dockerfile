# Not actually used by the devcontainer, but it is used by gitpod
ARG VARIANT=17-bullseye
FROM mcr.microsoft.com/vscode/devcontainers/java:0-${VARIANT}
ARG NODE_VERSION="none"
RUN if [ "${NODE_VERSION}" != "none" ]; then su vscode -c "umask 0002 && . /usr/local/share/nvm/nvm.sh && nvm install ${NODE_VERSION} 2>&1"; fi
ARG USER=vscode
VOLUME /home/<USER>/.m2
VOLUME /home/<USER>/.gradle
ARG JAVA_VERSION=17.0.7-ms
RUN sudo mkdir /home/<USER>/.m2 /home/<USER>/.gradle && sudo chown $USER:$USER /home/<USER>/.m2 /home/<USER>/.gradle
RUN bash -lc '. /usr/local/sdkman/bin/sdkman-init.sh && sdk install java $JAVA_VERSION && sdk use java $JAVA_VERSION'